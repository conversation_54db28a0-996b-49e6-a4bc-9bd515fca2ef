<template>
  <div class="workflow-timeline">
    <!-- 工作领导人选择器 -->
    <div class="leader-selector">
      <span class="label">工作领导人</span>
      <Select
        v-model:value="selectedLeader"
        class="selector"
        placeholder="请选择工作领导人"
        @change="handleLeaderChange"
      >
        <SelectOption
          v-for="leader in leaders"
          :key="leader.id"
          :value="leader.id"
        >
          {{ leader.name }}
        </SelectOption>
      </Select>
      <span class="count">{{ completedCount }}/{{ totalCount }}</span>
    </div>

    <!-- 时间线内容 -->
    <div class="timeline-content">
      <!-- 已完成流程 -->
      <div v-if="completedSteps.length > 0" class="completed-section">
        <div
          v-for="(step, index) in completedSteps"
          :key="step.id"
          class="timeline-item completed"
        >
          <div class="timeline-marker">
            <div class="step-number">{{ step.orderNum }}</div>
          </div>
          <div class="timeline-content-item">
            <div class="step-title">{{ step.workflowName }}</div>
            <div class="step-info">
              <span class="step-status">{{ step.workflowInfo }}</span>
            </div>
            <div class="attachments" v-if="getStepAttachments(step).length > 0">
              <Icon icon="ant-design:paper-clip-outlined" class="attachment-icon" />
              <span
                v-for="(attachment, idx) in getStepAttachments(step)"
                :key="idx"
                class="attachment-item"
                @click="handleAttachmentClick(attachment)"
              >
                {{ attachment.name }}
              </span>
            </div>
            <div class="step-time">{{ formatTime(step.endTime || step.startTime) }}</div>
          </div>
        </div>
      </div>

      <!-- 未完成流程 -->
      <div v-if="pendingSteps.length > 0" class="pending-section">
        <div class="section-title">未完成流程</div>
        <div class="pending-count">{{ pendingSteps.length }}</div>
        <div
          v-for="(step, index) in pendingSteps"
          :key="step.id"
          class="timeline-item pending"
        >
          <div class="timeline-marker">
            <div class="step-number">{{ step.orderNum }}</div>
          </div>
          <div class="timeline-content-item">
            <div class="step-title">{{ step.workflowName }}</div>
            <div class="step-time">{{ formatTime(step.startTime) }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, watch, onMounted } from 'vue';
  import { Select, SelectOption } from 'ant-design-vue';
  import Icon from '@/components/Icon/Icon.vue';
  import { getControlTaskWorkflowList } from '@/api/security/railwayWorkflowTaskStep/index';
  import { formatToDateTime } from '@/utils/dateUtil';

  defineOptions({ name: 'WorkflowTimeline' });

  interface WorkflowStep {
    id: string | number;
    taskId: string | number;
    workflowId: string | number;
    workflowName: string;
    workflowInfo: string;
    orderNum: number;
    startTime: string;
    endTime: string;
    durationMinutes: number;
    personChargeId: string | number;
    personChargeName: string;
    personChargeRoleType: string;
    status: string; // '1': 未开始, '2': 进行中, '3': 已结束
    isMultipleAttachments: string;
    arGlassId: string;
    remark: string;
    attachments?: Array<{ name: string; url: string }>;
  }

  interface Leader {
    id: string | number;
    name: string;
  }

  const props = defineProps({
    params: {
      type: Object,
      default: () => ({}),
    },
  });

  // 响应式数据
  const selectedLeader = ref<string | number>();
  const leaders = ref<Leader[]>([]);
  const workflowSteps = ref<WorkflowStep[]>([]);
  const loading = ref(false);

  // 计算属性
  const completedSteps = computed(() => {
    return workflowSteps.value
      .filter(step => step.status === '3')
      .sort((a, b) => a.orderNum - b.orderNum);
  });

  const pendingSteps = computed(() => {
    return workflowSteps.value
      .filter(step => step.status !== '3')
      .sort((a, b) => a.orderNum - b.orderNum);
  });

  const completedCount = computed(() => completedSteps.value.length);
  const totalCount = computed(() => workflowSteps.value.length);

  // 方法
  function formatTime(timeStr: string) {
    if (!timeStr) return '';
    return formatToDateTime(timeStr);
  }

  function getStepAttachments(step: WorkflowStep) {
    // 如果步骤有附件数据，返回附件
    if (step.attachments && step.attachments.length > 0) {
      return step.attachments;
    }

    // 根据步骤状态和类型模拟一些附件数据（仅用于演示）
    if (step.status === '3' && step.workflowName.includes('申核工作票')) {
      return [
        { name: '张三上传附件1', url: '#' },
        { name: '张三上传附件1', url: '#' }
      ];
    }

    return [];
  }

  function handleAttachmentClick(attachment: { name: string; url: string }) {
    console.log('点击附件:', attachment);
    // 这里可以添加附件下载或预览逻辑
  }

  function handleLeaderChange(value: string | number) {
    selectedLeader.value = value;
    loadAllWorkflowSteps();
  }

  async function loadWorkflowSteps() {
    if (!props.params.taskId) return;

    loading.value = true;
    try {
      const response = await getControlTaskWorkflowList({
        ...props.params,
        personChargeId: selectedLeader.value,
      });

      workflowSteps.value = response.rows || response || [];

      // 如果是首次加载，提取领导人列表
      if (leaders.value.length === 0) {
        const uniqueLeaders = new Map();
        workflowSteps.value.forEach(step => {
          if (step.personChargeId && step.personChargeName) {
            uniqueLeaders.set(step.personChargeId, {
              id: step.personChargeId,
              name: step.personChargeName,
            });
          }
        });
        leaders.value = Array.from(uniqueLeaders.values());

        // 如果没有选中的领导人，默认选择第一个
        if (!selectedLeader.value && leaders.value.length > 0) {
          selectedLeader.value = leaders.value[0].id;
        }
      }
    } catch (error) {
      console.error('加载流程步骤失败:', error);
      workflowSteps.value = [];
    } finally {
      loading.value = false;
    }
  }

  async function loadAllWorkflowSteps() {
    if (!props.params.taskId) return;

    loading.value = true;
    try {
      const response = await getControlTaskWorkflowList({
        ...props.params,
      });

      const allSteps = response.rows || response || [];

      // 提取领导人列表
      const uniqueLeaders = new Map();
      allSteps.forEach(step => {
        if (step.personChargeId && step.personChargeName) {
          uniqueLeaders.set(step.personChargeId, {
            id: step.personChargeId,
            name: step.personChargeName,
          });
        }
      });
      leaders.value = Array.from(uniqueLeaders.values());

      // 如果没有选中的领导人，默认选择第一个
      if (!selectedLeader.value && leaders.value.length > 0) {
        selectedLeader.value = leaders.value[0].id;
        // 过滤当前选中领导人的步骤
        workflowSteps.value = allSteps.filter(step =>
          step.personChargeId === selectedLeader.value
        );
      } else {
        workflowSteps.value = allSteps.filter(step =>
          step.personChargeId === selectedLeader.value
        );
      }
    } catch (error) {
      console.error('加载流程步骤失败:', error);
      workflowSteps.value = [];
    } finally {
      loading.value = false;
    }
  }
  
  onMounted(() => {
    loadAllWorkflowSteps();
  });
</script>

<style scoped>
.workflow-timeline {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #fff;
}

.leader-selector {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px 24px 0;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.label {
  margin-right: 8px;
  color: #262626;
  font-size: 14px;
  font-weight: 400;
  white-space: nowrap;
}

.selector {
  flex: 1;
  max-width: 180px;
  margin-right: 16px;
}

.selector :deep(.ant-select-selector) {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
}

.count {
  color: #8c8c8c;
  font-size: 14px;
  white-space: nowrap;
}

.timeline-content {
  flex: 1;
  padding: 20px 24px;
  overflow-y: auto;
}

.completed-section {
  margin-bottom: 40px;
}

.pending-section {
  position: relative;
}

.section-title {
  position: relative;
  margin-bottom: 20px;
  color: #262626;
  font-size: 16px;
  font-weight: 500;
}

.pending-count {
  position: absolute;
  top: 0;
  right: 0;
  min-width: 20px;
  padding: 2px 8px;
  border-radius: 10px;
  background: #f5f5f5;
  color: #8c8c8c;
  font-size: 14px;
  text-align: center;
}

.timeline-item {
  display: flex;
  position: relative;
  margin-bottom: 32px;
}

.timeline-item:not(:last-child)::after {
  content: '';
  position: absolute;
  top: 32px;
  bottom: -32px;
  left: 15px;
  width: 1px;
  background-color: #f0f0f0;
}

.timeline-marker {
  position: relative;
  z-index: 1;
  flex-shrink: 0;
  margin-right: 16px;
}

.step-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 10%);
  color: white;
  font-size: 14px;
  font-weight: 500;
}

.completed .step-number {
  background-color: #1890ff;
}

.pending .step-number {
  background-color: #bfbfbf;
  color: #fff;
}

.timeline-content-item {
  flex: 1;
  padding-top: 2px;
}

.step-title {
  margin-bottom: 8px;
  color: #262626;
  font-size: 15px;
  font-weight: 500;
  line-height: 1.4;
}

.step-info {
  margin-bottom: 8px;
}

.step-status {
  color: #595959;
  font-size: 13px;
  line-height: 1.4;
}

.attachments {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-bottom: 8px;
  gap: 8px;
}

.attachment-icon {
  color: #1890ff;
  font-size: 14px;
}

.attachment-item {
  padding: 2px 6px;
  transition: all 0.2s;
  border: 1px solid #d6e4ff;
  border-radius: 4px;
  background: #f0f8ff;
  color: #1890ff;
  font-size: 13px;
  cursor: pointer;
}

.attachment-item:hover {
  border-color: #91d5ff;
  background: #e6f7ff;
}

.step-time {
  margin-top: 4px;
  color: #8c8c8c;
  font-size: 12px;
}

/* 滚动条样式 */
.timeline-content::-webkit-scrollbar {
  width: 4px;
}

.timeline-content::-webkit-scrollbar-track {
  background: transparent;
}

.timeline-content::-webkit-scrollbar-thumb {
  border-radius: 2px;
  background: #d9d9d9;
}

.timeline-content::-webkit-scrollbar-thumb:hover {
  background: #bfbfbf;
}
</style>
